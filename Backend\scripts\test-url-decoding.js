/**
 * Test script to verify URL decoding fix for S3 keys
 */

require('dotenv').config();
const { validateAndExtractS3Key } = require('../utils/previewGenerator');

/**
 * Test URL decoding functionality
 */
function testUrlDecoding() {
  console.log('🔧 Testing URL Decoding Fix for S3 Keys\n');

  const testCases = [
    {
      name: 'URL with encoded slashes',
      url: 'https://xosports.s3.amazonaws.com/uploads%2Fcontent%2F1752869820970-videoplayback.mp4',
      expectedKey: 'uploads/content/1752869820970-videoplayback.mp4',
      description: 'Common case from chunked upload'
    },
    {
      name: 'URL with encoded spaces',
      url: 'https://xosports.s3.amazonaws.com/uploads%2Fcontent%2Fmy%20video%20file.mp4',
      expectedKey: 'uploads/content/my video file.mp4',
      description: 'File with spaces in name'
    },
    {
      name: 'URL with multiple encoded characters',
      url: 'https://xosports.s3.amazonaws.com/uploads%2Fcontent%2F2024%2Ftest%20file%20%28copy%29.mp4',
      expectedKey: 'uploads/content/2024/test file (copy).mp4',
      description: 'Complex encoding scenario'
    },
    {
      name: 'Normal URL without encoding',
      url: 'https://xosports.s3.amazonaws.com/uploads/content/normal-file.mp4',
      expectedKey: 'uploads/content/normal-file.mp4',
      description: 'Standard case without encoding'
    },
    {
      name: 'URL with bucket in subdomain',
      url: 'https://xosports.s3.us-east-1.amazonaws.com/uploads%2Fcontent%2Ftest.mp4',
      expectedKey: 'uploads/content/test.mp4',
      description: 'Regional S3 URL format'
    }
  ];

  const bucketName = 'xosports';
  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   📝 Description: ${testCase.description}`);
    console.log(`   🔗 Input URL: ${testCase.url}`);
    console.log(`   🎯 Expected Key: ${testCase.expectedKey}`);

    try {
      const extractedKey = validateAndExtractS3Key(testCase.url, bucketName);
      
      if (extractedKey === testCase.expectedKey) {
        console.log(`   ✅ PASS: Extracted key matches expected`);
        console.log(`   📤 Actual Key: ${extractedKey}`);
        passedTests++;
      } else {
        console.log(`   ❌ FAIL: Key mismatch`);
        console.log(`   📤 Actual Key: ${extractedKey}`);
        console.log(`   📥 Expected Key: ${testCase.expectedKey}`);
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }
    
    console.log('');
  });

  console.log('📊 Test Results:');
  console.log(`   ✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`   ❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('   🎉 All tests passed! URL decoding fix is working correctly.');
  } else {
    console.log('   ⚠️  Some tests failed. Please review the implementation.');
  }
  
  return passedTests === totalTests;
}

/**
 * Test the specific error case from the logs
 */
function testSpecificErrorCase() {
  console.log('\n🐛 Testing Specific Error Case from Logs\n');
  
  const problematicUrl = 'https://xosports.s3.amazonaws.com/uploads%2Fcontent%2F1752869820970-videoplayback.mp4';
  const bucketName = 'xosports';
  const expectedKey = 'uploads/content/1752869820970-videoplayback.mp4';
  
  console.log('🔍 Reproducing the exact error scenario:');
  console.log(`   📁 Bucket: ${bucketName}`);
  console.log(`   🔗 URL: ${problematicUrl}`);
  console.log(`   🎯 Expected: ${expectedKey}`);
  console.log('');
  
  try {
    console.log('🔧 Extracting S3 key...');
    const extractedKey = validateAndExtractS3Key(problematicUrl, bucketName);
    
    console.log(`   📤 Extracted Key: ${extractedKey}`);
    
    if (extractedKey === expectedKey) {
      console.log('   ✅ SUCCESS: The URL decoding fix resolves the original error!');
      console.log('   🎯 The S3 key is now correctly decoded for file lookup.');
      return true;
    } else {
      console.log('   ❌ ISSUE: Key still doesn\'t match expected value');
      console.log(`   📥 Expected: ${expectedKey}`);
      console.log(`   📤 Actual: ${extractedKey}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Show before/after comparison
 */
function showBeforeAfterComparison() {
  console.log('\n📈 Before/After Comparison\n');
  
  const testUrl = 'https://xosports.s3.amazonaws.com/uploads%2Fcontent%2F1752869820970-videoplayback.mp4';
  
  console.log('❌ BEFORE (without URL decoding):');
  console.log('   🔗 Input: ' + testUrl);
  console.log('   📤 Extracted: uploads%2Fcontent%2F1752869820970-videoplayback.mp4');
  console.log('   🚫 S3 Lookup: NotFound error (key doesn\'t exist with encoded characters)');
  console.log('');
  
  console.log('✅ AFTER (with URL decoding):');
  console.log('   🔗 Input: ' + testUrl);
  console.log('   📤 Extracted: uploads/content/1752869820970-videoplayback.mp4');
  console.log('   ✅ S3 Lookup: Success (key exists with proper path separators)');
  console.log('');
  
  console.log('🎯 Key Improvements:');
  console.log('   • URL-encoded characters (%2F) are properly decoded to (/)');
  console.log('   • S3 key lookup now uses the correct path format');
  console.log('   • Retry logic added for temporary S3 availability issues');
  console.log('   • Better error messages for debugging');
  console.log('   • Graceful fallback mechanisms');
}

/**
 * Main test function
 */
function runTests() {
  console.log('🧪 URL Decoding Fix Verification\n');
  console.log('=' * 50);
  
  const urlTestsPassed = testUrlDecoding();
  const specificTestPassed = testSpecificErrorCase();
  
  showBeforeAfterComparison();
  
  console.log('\n🎯 Final Results:');
  if (urlTestsPassed && specificTestPassed) {
    console.log('   ✅ All tests passed! The URL decoding fix is working correctly.');
    console.log('   🚀 Large video uploads should now work without S3 key errors.');
    console.log('   📊 Preview generation will use optimized partial downloads.');
  } else {
    console.log('   ❌ Some tests failed. Please review the implementation.');
  }
  
  return urlTestsPassed && specificTestPassed;
}

// Run tests
if (require.main === module) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = {
  testUrlDecoding,
  testSpecificErrorCase,
  showBeforeAfterComparison
};
